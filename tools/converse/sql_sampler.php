<?php

// open ssh tunnel to db server before running!

# TODO: get dotenv working
# TODO: Make record amount a variable
# TODO: Add cli args for table name, output file name, record count
// Pull from multiple tables or schema versions?
// Detect changes across samples (data drift detection)?

// database access
$host = '127.0.0.1';
$port = '3307';
$dbname = getenv('DB_NAME');
$user = getenv('DB_USER');
$pass = getenv('DB_PASS');

// query
$modulus = 1000;
$targetBucket = 1; // change this number to get a different sample
// $targetBucket = rand(0, $modulus - 1); // use this to get a random sample
$sampleSize = 5000;

// output
$outputFile = 'cv_slice.csv'; // append record count to this

try {
    // Set up PDO connection
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $user, $pass);

    // Set error mode
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Prepare your query
    $sql = "
        SELECT *
        FROM Converse
        WHERE MOD(CONV(SUBSTRING(MD5(`STHHLD`), 1, 8), 16, 10), 1000) = 1
        LIMIT 5000
    ";

    // Execute the query
    $stmt = $pdo->query($sql);
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($rows)) {
        echo "No rows returned.\n";
        exit;
    }

    // Open CSV file for writing
    $fp = fopen($outputFile, 'w');

    // Write header
    fputcsv($fp, array_keys($rows[0]));

    // Write each row
    foreach ($rows as $row) {
        fputcsv($fp, $row);
    }

    fclose($fp);

    echo "Sample exported to $outputFile\n";

} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage();
    exit;
}
